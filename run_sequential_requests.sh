#!/bin/bash

# 脚本：按顺序读取data文件夹中的txt文件并发送API请求
# 每个文件处理完后需要手动输入"next"继续

# 设置变量
API_URL="192.168.2.31:8000/v2/chat/completions"
MODEL="Qwen/Qwen2.5-1.5B-Instruct"
DATA_DIR="lmcache-vllm-extended/frontend/data"

# 获取所有txt文件并排序
txt_files=($(ls "$DATA_DIR"/*.txt | sort))

echo "找到 ${#txt_files[@]} 个txt文件"
echo "开始按顺序处理文件..."
echo "注意：每处理完一个文件后，需要输入 'next' 继续下一个文件"
echo "========================================================"

# 计数器
counter=1

# 遍历每个txt文件
for file in "${txt_files[@]}"; do
    echo ""
    echo "第 $counter 个文件: $(basename "$file")"
    echo "----------------------------------------"
    
    # 读取文件内容
    if [ ! -f "$file" ]; then
        echo "错误：文件 $file 不存在"
        continue
    fi
    
    content=$(cat "$file")
    
    echo "文件内容长度: $(echo "$content" | wc -c) 字符"
    echo "内容预览: $(echo "$content" | head -c 150)..."
    
    # 转义JSON中的特殊字符
    escaped_content=$(echo "$content" | sed 's/\\/\\\\/g' | sed 's/"/\\"/g' | sed 's/$/\\n/' | tr -d '\n' | sed 's/\\n$//')
    
    # 构建JSON请求体
    json_data=$(cat <<EOF
{
    "model": "$MODEL",
    "messages": [
        {"role": "user", "content": "$escaped_content"}
    ],
    "max_tokens": 1000
}
EOF
)
    
    echo ""
    echo "发送请求到 $API_URL"
    
    # 发送curl请求并直接显示响应
    response=$(curl -X POST "$API_URL" \
        -H "Content-Type: application/json" \
        -d "$json_data" \
        -w "\n\nHTTP状态码: %{http_code}\n响应时间: %{time_total}s\n" \
        --connect-timeout 30 \
        --max-time 300 \
        -s)
    
    if [ $? -eq 0 ]; then
        echo "✓ 请求成功完成"
        
        # 尝试解析并显示响应内容
        if command -v jq &> /dev/null; then
            echo ""
            echo "响应内容:"
            echo "----------------------------------------"
            echo "$response" | jq -r '.choices[0].message.content // .error // "无法解析响应"' 2>/dev/null
            echo "----------------------------------------"
        else
            echo ""
            echo "完整响应:"
            echo "----------------------------------------"
            echo "$response"
            echo "----------------------------------------"
        fi
    else
        echo "✗ 请求失败"
    fi
    
    ((counter++))
    
    # 如果不是最后一个文件，等待用户输入
    if [ $counter -le ${#txt_files[@]} ] && [ $counter -le 15 ]; then
        echo ""
        echo "已完成第 $((counter-1)) 个文件的处理"
        while true; do
            read -p "输入 'next' 继续下一个文件，或 'quit' 退出: " user_input
            case $user_input in
                next|NEXT)
                    break
                    ;;
                quit|QUIT)
                    echo "用户选择退出"
                    exit 0
                    ;;
                *)
                    echo "请输入 'next' 或 'quit'"
                    ;;
            esac
        done
    fi
    
    # 如果已处理15个文件，退出循环
    if [ $counter -gt 15 ]; then
        break
    fi
done

echo ""
echo "========================================================"
echo "所有文件处理完成！"
echo "========================================================"
