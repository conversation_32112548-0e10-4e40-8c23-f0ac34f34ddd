from safetensors.torch import load_file
from typing import Dict
import os
import numpy as np
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
from sklearn.cluster import KMeans
from matplotlib.patches import Ellipse

def read_cache(file_folder) -> Dict[str, list]: # list.shape: [3584]
    """
    Read all the txt files in the folder and return the filenames
    """
    filenames = os.listdir(file_folder)
    ret = {}
    for filename in filenames:
        if not filename.endswith(".bin"):
            continue
        kv_cache = load_file(os.path.join(file_folder, filename))
        kv = kv_cache['tensor_bytes']

        value_tensor = kv[:, 1]  # shape: [28, 35, 2, 128]

        # 我们可以对 seq_len 和 heads 做平均，得到每层一个 [128] 向量
        value_pooled = value_tensor.mean(dim=1).mean(dim=1)  # shape: [28, 128]

        final_vector = value_pooled.flatten()  # shape: [3584]
        key = filename.removesuffix(".bin")

        ret[key] = final_vector.tolist()  # 转换为列表形式

    return ret

caches = read_cache("context_cache/")

pcaCache = caches  # 这里直接使用 caches，已经是降维后的向量

def visualize_cache(vectors: Dict[str, list], n_components: int = 2):
    """
    Visualize the clusters using t-SNE and save the figure as a PNG file.
    """
    all_vectors = list(vectors.values())
    all_vectors = np.array(all_vectors)
    
    perplexity_val = min(5, len(vectors) - 1)  # 设置perplexity小于样本数
    
    tsne = TSNE(n_components=n_components, random_state=42, perplexity=perplexity_val)
    
    # Fit t-SNE on the data
    reduced_vectors = tsne.fit_transform(all_vectors)
    
    plt.figure(figsize=(10, 8))
    for i, (key, vec) in enumerate(vectors.items()):
        plt.scatter(reduced_vectors[i, 0], reduced_vectors[i, 1], label=key)
    
    plt.title("t-SNE Visualization of Clusters")
    plt.xlabel("Component 1")
    plt.ylabel("Component 2")
    plt.legend()
    
    plt.savefig("tsne_clusters.png", dpi=300)  # 保存图片，dpi可以调节分辨率
    plt.show()
visualize_cache(pcaCache, n_components=2)

def cluster_vectors(vectors: dict, n_clusters: int = 2):
    kmeans = KMeans(n_clusters=n_clusters, random_state=42)
    all_vectors = list(vectors.values())
    labels = kmeans.fit_predict(all_vectors)
    clusters = {i: [] for i in range(n_clusters)}
    for i, key in enumerate(vectors.keys()):
        clusters[labels[i]].append(key)
    return labels, clusters

def visualize_clusters(vectors: dict, labels: np.ndarray, n_components: int = 2):
    all_vectors = np.array(list(vectors.values()))
    perplexity_val = min(5, len(vectors) - 1)
    tsne = TSNE(n_components=n_components, random_state=42, perplexity=perplexity_val)
    reduced_vectors = tsne.fit_transform(all_vectors)

    plt.figure(figsize=(10, 8))
    scatter = plt.scatter(reduced_vectors[:, 0], reduced_vectors[:, 1], c=labels, cmap='tab10', s=50)

    # 为每个簇绘制椭圆圈
    for cluster_id in np.unique(labels):
        points = reduced_vectors[labels == cluster_id]
        if points.shape[0] > 1:
            cov = np.cov(points, rowvar=False)
            mean = points.mean(axis=0)
            eigenvals, eigenvecs = np.linalg.eigh(cov)
            order = eigenvals.argsort()[::-1]
            eigenvals, eigenvecs = eigenvals[order], eigenvecs[:, order]
            angle = np.degrees(np.arctan2(*eigenvecs[:, 0][::-1]))
            width, height = 2 * np.sqrt(eigenvals)
            ellipse = Ellipse(mean, width, height, angle=angle, edgecolor='black', facecolor='none', linewidth=2)

            plt.gca().add_patch(ellipse)

    plt.title("t-SNE Visualization with KMeans Clusters")
    plt.xlabel("Component 1")
    plt.ylabel("Component 2")
    plt.colorbar(scatter, ticks=np.unique(labels))
    plt.savefig("tsne_clusters_with_ellipses.png", dpi=300)
    plt.show()

# 假设pcaCache是你的降维后的向量字典
labels, clusters = cluster_vectors(pcaCache, n_clusters=2)
print("Clusters:")
for cid, keys in clusters.items():
    print(f"Cluster {cid}: {', '.join(keys)}")

visualize_clusters(pcaCache, labels, n_components=2)


