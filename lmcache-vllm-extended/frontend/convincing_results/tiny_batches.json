{"experiment_config": {"num_batches": 8, "batch_size": 3, "repeat_ratio": 0.4, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null, "api_version": "v1", "scheduling": "client-side"}, "metrics": {"total_requests": 24, "successful_requests": 24, "failed_requests": 0, "average_latency": 0.5233296354611715, "total_latency": 12.559911251068115, "throughput": 1.9108415274796628}, "detailed_results": [{"request_id": "req_1", "context_hash": "5374302584950299945", "sequence_length": 496, "latency": 0.191680908203125, "success": true, "timestamp": 1748596904.3749168, "batch_id": "batch_4373", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "2604027849650463491", "sequence_length": 465, "latency": 1.041029691696167, "success": true, "timestamp": 1748596904.566635, "batch_id": "batch_4373", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "2604027849650463491", "sequence_length": 469, "latency": 0.878483772277832, "success": true, "timestamp": 1748596905.607703, "batch_id": "batch_4373", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "2366700791715649595", "sequence_length": 480, "latency": 0.07633519172668457, "success": true, "timestamp": 1748596906.486398, "batch_id": "batch_4373", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "59627733663915969", "sequence_length": 533, "latency": 0.5271844863891602, "success": true, "timestamp": 1748596906.5627635, "batch_id": "batch_4373", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "2366700791715649595", "sequence_length": 482, "latency": 0.7559444904327393, "success": true, "timestamp": 1748596907.0899775, "batch_id": "batch_4373", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "4844086234001476807", "sequence_length": 546, "latency": 0.07415986061096191, "success": true, "timestamp": 1748596907.8461008, "batch_id": "batch_4373", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "4844086234001476807", "sequence_length": 543, "latency": 0.09891057014465332, "success": true, "timestamp": 1748596907.920288, "batch_id": "batch_4373", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "59627733663915969", "sequence_length": 535, "latency": 0.30901050567626953, "success": true, "timestamp": 1748596908.0192266, "batch_id": "batch_4373", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "4844086234001476807", "sequence_length": 546, "latency": 0.050171613693237305, "success": true, "timestamp": 1748596908.3283892, "batch_id": "batch_4373", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "860793566935668255", "sequence_length": 556, "latency": 0.07343769073486328, "success": true, "timestamp": 1748596908.3785844, "batch_id": "batch_4373", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "860793566935668255", "sequence_length": 555, "latency": 0.8762822151184082, "success": true, "timestamp": 1748596908.45205, "batch_id": "batch_4373", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "4779945122393617320", "sequence_length": 539, "latency": 0.5310842990875244, "success": true, "timestamp": 1748596909.3284936, "batch_id": "batch_4373", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "4779945122393617320", "sequence_length": 540, "latency": 0.5630934238433838, "success": true, "timestamp": 1748596909.8596063, "batch_id": "batch_4373", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "2366700791715649595", "sequence_length": 482, "latency": 0.7932953834533691, "success": true, "timestamp": 1748596910.4227324, "batch_id": "batch_4373", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "-5513473145303573429", "sequence_length": 502, "latency": 0.07170248031616211, "success": true, "timestamp": 1748596911.2162004, "batch_id": "batch_4373", "batch_index": 5, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-6133293624496653724", "sequence_length": 512, "latency": 0.22289443016052246, "success": true, "timestamp": 1748596911.2879314, "batch_id": "batch_4373", "batch_index": 5, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-6133293624496653724", "sequence_length": 512, "latency": 0.22684073448181152, "success": true, "timestamp": 1748596911.5108547, "batch_id": "batch_4373", "batch_index": 5, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "59627733663915969", "sequence_length": 535, "latency": 0.4719882011413574, "success": true, "timestamp": 1748596911.7378416, "batch_id": "batch_4373", "batch_index": 6, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-6133293624496653724", "sequence_length": 512, "latency": 1.4716649055480957, "success": true, "timestamp": 1748596912.2098522, "batch_id": "batch_4373", "batch_index": 6, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-6133293624496653724", "sequence_length": 515, "latency": 0.23093962669372559, "success": true, "timestamp": 1748596913.681548, "batch_id": "batch_4373", "batch_index": 6, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "8097783737844342201", "sequence_length": 486, "latency": 0.07779669761657715, "success": true, "timestamp": 1748596913.9126558, "batch_id": "batch_4373", "batch_index": 7, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "8097783737844342201", "sequence_length": 486, "latency": 0.05305790901184082, "success": true, "timestamp": 1748596913.990494, "batch_id": "batch_4373", "batch_index": 7, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "4784531119753820689", "sequence_length": 12827, "latency": 2.8929221630096436, "success": true, "timestamp": 1748596914.0435867, "batch_id": "batch_4373", "batch_index": 7, "processing_mode": "sequential"}]}