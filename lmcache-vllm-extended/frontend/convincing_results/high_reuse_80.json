{"experiment_config": {"num_batches": 5, "batch_size": 10, "repeat_ratio": 0.8, "diversity_level": 0.2, "processing_mode": "sequential", "max_concurrent": null, "api_version": "v1", "scheduling": "client-side"}, "metrics": {"total_requests": 50, "successful_requests": 50, "failed_requests": 0, "average_latency": 0.8761038875579834, "total_latency": 43.80519437789917, "throughput": 1.1414171472145382}, "detailed_results": [{"request_id": "req_1", "context_hash": "5247436089720097328", "sequence_length": 468, "latency": 0.2961275577545166, "success": true, "timestamp": 1748596412.5220466, "batch_id": "batch_12517", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "5247436089720097328", "sequence_length": 468, "latency": 0.14199090003967285, "success": true, "timestamp": 1748596412.8182151, "batch_id": "batch_12517", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "5247436089720097328", "sequence_length": 469, "latency": 0.1497185230255127, "success": true, "timestamp": 1748596412.9602396, "batch_id": "batch_12517", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "5247436089720097328", "sequence_length": 466, "latency": 0.17811179161071777, "success": true, "timestamp": 1748596413.1099896, "batch_id": "batch_12517", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "158327859093066947", "sequence_length": 533, "latency": 0.5016319751739502, "success": true, "timestamp": 1748596413.2881334, "batch_id": "batch_12517", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "158327859093066947", "sequence_length": 532, "latency": 0.4861626625061035, "success": true, "timestamp": 1748596413.7897964, "batch_id": "batch_12517", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "5247436089720097328", "sequence_length": 466, "latency": 0.1923050880432129, "success": true, "timestamp": 1748596414.275994, "batch_id": "batch_12517", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "5247436089720097328", "sequence_length": 466, "latency": 0.11287689208984375, "success": true, "timestamp": 1748596414.4683287, "batch_id": "batch_12517", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "158327859093066947", "sequence_length": 536, "latency": 0.6763153076171875, "success": true, "timestamp": 1748596414.581235, "batch_id": "batch_12517", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "5247436089720097328", "sequence_length": 469, "latency": 0.06815528869628906, "success": true, "timestamp": 1748596415.257581, "batch_id": "batch_12517", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "1779492853954607313", "sequence_length": 12831, "latency": 2.9032320976257324, "success": true, "timestamp": 1748596415.3259573, "batch_id": "batch_12517", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-346111698923655514", "sequence_length": 505, "latency": 1.5468683242797852, "success": true, "timestamp": 1748596418.2292244, "batch_id": "batch_12517", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "1779492853954607313", "sequence_length": 12831, "latency": 2.729773998260498, "success": true, "timestamp": 1748596419.7761276, "batch_id": "batch_12517", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-346111698923655514", "sequence_length": 508, "latency": 1.5316615104675293, "success": true, "timestamp": 1748596422.505937, "batch_id": "batch_12517", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-346111698923655514", "sequence_length": 504, "latency": 1.6029551029205322, "success": true, "timestamp": 1748596424.0376306, "batch_id": "batch_12517", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "1779492853954607313", "sequence_length": 12828, "latency": 2.8000407218933105, "success": true, "timestamp": 1748596425.6406183, "batch_id": "batch_12517", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "1779492853954607313", "sequence_length": 12831, "latency": 2.722656488418579, "success": true, "timestamp": 1748596428.4406939, "batch_id": "batch_12517", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "1779492853954607313", "sequence_length": 12830, "latency": 2.761432409286499, "success": true, "timestamp": 1748596431.1633847, "batch_id": "batch_12517", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "1779492853954607313", "sequence_length": 12827, "latency": 2.718402862548828, "success": true, "timestamp": 1748596433.9248521, "batch_id": "batch_12517", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "1779492853954607313", "sequence_length": 12830, "latency": 2.7679784297943115, "success": true, "timestamp": 1748596436.643287, "batch_id": "batch_12517", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "3881304022355021278", "sequence_length": 495, "latency": 0.07795834541320801, "success": true, "timestamp": 1748596439.4114757, "batch_id": "batch_12521", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "7050319027084032597", "sequence_length": 448, "latency": 0.2822601795196533, "success": true, "timestamp": 1748596439.4894645, "batch_id": "batch_12521", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "7050319027084032597", "sequence_length": 448, "latency": 0.12714457511901855, "success": true, "timestamp": 1748596439.7717595, "batch_id": "batch_12521", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "7050319027084032597", "sequence_length": 451, "latency": 0.11778926849365234, "success": true, "timestamp": 1748596439.8989356, "batch_id": "batch_12521", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "3881304022355021278", "sequence_length": 492, "latency": 0.05487537384033203, "success": true, "timestamp": 1748596440.0167503, "batch_id": "batch_12521", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "3881304022355021278", "sequence_length": 496, "latency": 0.053442955017089844, "success": true, "timestamp": 1748596440.0716493, "batch_id": "batch_12521", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "3881304022355021278", "sequence_length": 493, "latency": 0.0692434310913086, "success": true, "timestamp": 1748596440.1251159, "batch_id": "batch_12521", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "7050319027084032597", "sequence_length": 451, "latency": 0.11162257194519043, "success": true, "timestamp": 1748596440.194383, "batch_id": "batch_12521", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "3881304022355021278", "sequence_length": 492, "latency": 0.05118107795715332, "success": true, "timestamp": 1748596440.3060272, "batch_id": "batch_12521", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "3881304022355021278", "sequence_length": 495, "latency": 0.053850412368774414, "success": true, "timestamp": 1748596440.3572292, "batch_id": "batch_12521", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "-5282980683674390145", "sequence_length": 483, "latency": 0.08748650550842285, "success": true, "timestamp": 1748596440.41124, "batch_id": "batch_12521", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-5282980683674390145", "sequence_length": 482, "latency": 0.05940961837768555, "success": true, "timestamp": 1748596440.4987595, "batch_id": "batch_12521", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-5282980683674390145", "sequence_length": 482, "latency": 0.04792046546936035, "success": true, "timestamp": 1748596440.5582001, "batch_id": "batch_12521", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-5282980683674390145", "sequence_length": 482, "latency": 0.04473090171813965, "success": true, "timestamp": 1748596440.6061437, "batch_id": "batch_12521", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "665782973865519476", "sequence_length": 459, "latency": 0.11458134651184082, "success": true, "timestamp": 1748596440.650896, "batch_id": "batch_12521", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-5282980683674390145", "sequence_length": 483, "latency": 0.06206345558166504, "success": true, "timestamp": 1748596440.7655027, "batch_id": "batch_12521", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "665782973865519476", "sequence_length": 459, "latency": 0.1248319149017334, "success": true, "timestamp": 1748596440.82759, "batch_id": "batch_12521", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "665782973865519476", "sequence_length": 459, "latency": 0.0936269760131836, "success": true, "timestamp": 1748596440.9524455, "batch_id": "batch_12521", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "-5282980683674390145", "sequence_length": 480, "latency": 0.0535736083984375, "success": true, "timestamp": 1748596441.046094, "batch_id": "batch_12521", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "665782973865519476", "sequence_length": 458, "latency": 0.11608171463012695, "success": true, "timestamp": 1748596441.099691, "batch_id": "batch_12521", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "-346111698923655514", "sequence_length": 508, "latency": 1.6868236064910889, "success": true, "timestamp": 1748596441.2159357, "batch_id": "batch_12521", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "1970688369553304750", "sequence_length": 487, "latency": 1.6944477558135986, "success": true, "timestamp": 1748596442.9027934, "batch_id": "batch_12521", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "1970688369553304750", "sequence_length": 483, "latency": 1.6738786697387695, "success": true, "timestamp": 1748596444.5972788, "batch_id": "batch_12521", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-346111698923655514", "sequence_length": 504, "latency": 1.6680943965911865, "success": true, "timestamp": 1748596446.2711902, "batch_id": "batch_12521", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "1970688369553304750", "sequence_length": 487, "latency": 1.6652448177337646, "success": true, "timestamp": 1748596447.9393182, "batch_id": "batch_12521", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "1970688369553304750", "sequence_length": 487, "latency": 1.6643497943878174, "success": true, "timestamp": 1748596449.6045978, "batch_id": "batch_12521", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "-346111698923655514", "sequence_length": 507, "latency": 1.6707556247711182, "success": true, "timestamp": 1748596451.2689822, "batch_id": "batch_12521", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "1970688369553304750", "sequence_length": 483, "latency": 1.6644835472106934, "success": true, "timestamp": 1748596452.9397738, "batch_id": "batch_12521", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "1970688369553304750", "sequence_length": 484, "latency": 0.06049990653991699, "success": true, "timestamp": 1748596454.6042922, "batch_id": "batch_12521", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "-346111698923655514", "sequence_length": 507, "latency": 1.664543628692627, "success": true, "timestamp": 1748596454.6648252, "batch_id": "batch_12521", "batch_index": 4, "processing_mode": "sequential"}]}