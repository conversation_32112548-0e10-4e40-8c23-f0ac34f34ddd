{"experiment_config": {"num_batches": 6, "batch_size": 8, "repeat_ratio": 0.7, "diversity_level": 0.1, "processing_mode": "sequential", "max_concurrent": null, "api_version": "v1", "scheduling": "client-side"}, "metrics": {"total_requests": 48, "successful_requests": 48, "failed_requests": 0, "average_latency": 0.7881427158912023, "total_latency": 37.83085036277771, "throughput": 1.2688057376375514}, "detailed_results": [{"request_id": "req_1", "context_hash": "-6997583751998175398", "sequence_length": 493, "latency": 0.06926822662353516, "success": true, "timestamp": 1748596738.3649, "batch_id": "batch_38363", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-6997583751998175398", "sequence_length": 495, "latency": 1.6777186393737793, "success": true, "timestamp": 1748596738.4342022, "batch_id": "batch_38363", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-6997583751998175398", "sequence_length": 492, "latency": 0.0760190486907959, "success": true, "timestamp": 1748596740.1119542, "batch_id": "batch_38363", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-6997583751998175398", "sequence_length": 492, "latency": 0.06148171424865723, "success": true, "timestamp": 1748596740.188003, "batch_id": "batch_38363", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-6997583751998175398", "sequence_length": 496, "latency": 1.6490211486816406, "success": true, "timestamp": 1748596740.2495053, "batch_id": "batch_38363", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-6997583751998175398", "sequence_length": 495, "latency": 0.07095932960510254, "success": true, "timestamp": 1748596741.8985515, "batch_id": "batch_38363", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "-6997583751998175398", "sequence_length": 492, "latency": 0.06599545478820801, "success": true, "timestamp": 1748596741.9695346, "batch_id": "batch_38363", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "-6997583751998175398", "sequence_length": 496, "latency": 1.6482977867126465, "success": true, "timestamp": 1748596742.0355504, "batch_id": "batch_38363", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "6581834831402587549", "sequence_length": 514, "latency": 0.37847208976745605, "success": true, "timestamp": 1748596743.6840608, "batch_id": "batch_38363", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "6581834831402587549", "sequence_length": 514, "latency": 0.1612999439239502, "success": true, "timestamp": 1748596744.0625648, "batch_id": "batch_38363", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "6581834831402587549", "sequence_length": 512, "latency": 0.21273589134216309, "success": true, "timestamp": 1748596744.2238886, "batch_id": "batch_38363", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "6581834831402587549", "sequence_length": 515, "latency": 0.166611909866333, "success": true, "timestamp": 1748596744.436648, "batch_id": "batch_38363", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "6581834831402587549", "sequence_length": 515, "latency": 0.17792463302612305, "success": true, "timestamp": 1748596744.6032832, "batch_id": "batch_38363", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "6581834831402587549", "sequence_length": 514, "latency": 0.1974353790283203, "success": true, "timestamp": 1748596744.7812333, "batch_id": "batch_38363", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "6581834831402587549", "sequence_length": 514, "latency": 0.42087864875793457, "success": true, "timestamp": 1748596744.978695, "batch_id": "batch_38363", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "6581834831402587549", "sequence_length": 515, "latency": 0.16202950477600098, "success": true, "timestamp": 1748596745.3995965, "batch_id": "batch_38363", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "-3896236376000182728", "sequence_length": 553, "latency": 1.6772961616516113, "success": true, "timestamp": 1748596745.5617917, "batch_id": "batch_38364", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-3896236376000182728", "sequence_length": 555, "latency": 1.6595535278320312, "success": true, "timestamp": 1748596747.2391188, "batch_id": "batch_38364", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-3896236376000182728", "sequence_length": 555, "latency": 1.6630146503448486, "success": true, "timestamp": 1748596748.8987045, "batch_id": "batch_38364", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-3896236376000182728", "sequence_length": 553, "latency": 1.6592888832092285, "success": true, "timestamp": 1748596750.5617502, "batch_id": "batch_38364", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-3896236376000182728", "sequence_length": 556, "latency": 1.6702916622161865, "success": true, "timestamp": 1748596752.2211137, "batch_id": "batch_38364", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-3896236376000182728", "sequence_length": 553, "latency": 1.6558516025543213, "success": true, "timestamp": 1748596753.891437, "batch_id": "batch_38364", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "-3896236376000182728", "sequence_length": 552, "latency": 1.6650032997131348, "success": true, "timestamp": 1748596755.5473194, "batch_id": "batch_38364", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "-3896236376000182728", "sequence_length": 553, "latency": 1.6554839611053467, "success": true, "timestamp": 1748596757.2123575, "batch_id": "batch_38364", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "6581834831402587549", "sequence_length": 514, "latency": 0.16654109954833984, "success": true, "timestamp": 1748596758.8680184, "batch_id": "batch_38364", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "6581834831402587549", "sequence_length": 514, "latency": 0.19840383529663086, "success": true, "timestamp": 1748596759.034588, "batch_id": "batch_38364", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "6581834831402587549", "sequence_length": 511, "latency": 0.21749067306518555, "success": true, "timestamp": 1748596759.233014, "batch_id": "batch_38364", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "6581834831402587549", "sequence_length": 512, "latency": 0.45391249656677246, "success": true, "timestamp": 1748596759.4505277, "batch_id": "batch_38364", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "6581834831402587549", "sequence_length": 511, "latency": 0.4417898654937744, "success": true, "timestamp": 1748596759.9044673, "batch_id": "batch_38364", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "6581834831402587549", "sequence_length": 511, "latency": 0.4384279251098633, "success": true, "timestamp": 1748596760.346282, "batch_id": "batch_38364", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "6581834831402587549", "sequence_length": 512, "latency": 0.17825078964233398, "success": true, "timestamp": 1748596760.7847323, "batch_id": "batch_38364", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "6581834831402587549", "sequence_length": 514, "latency": 0.16231822967529297, "success": true, "timestamp": 1748596760.963003, "batch_id": "batch_38364", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "-1297287476409344247", "sequence_length": 469, "latency": 0.38994622230529785, "success": true, "timestamp": 1748596761.1254706, "batch_id": "batch_38364", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-1297287476409344247", "sequence_length": 465, "latency": 0.523221492767334, "success": true, "timestamp": 1748596761.5154457, "batch_id": "batch_38364", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-1297287476409344247", "sequence_length": 468, "latency": 0.15258145332336426, "success": true, "timestamp": 1748596762.0386949, "batch_id": "batch_38364", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-1297287476409344247", "sequence_length": 466, "latency": 0.23181509971618652, "success": true, "timestamp": 1748596762.191301, "batch_id": "batch_38364", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-1297287476409344247", "sequence_length": 469, "latency": 0.0655527114868164, "success": true, "timestamp": 1748596762.4231448, "batch_id": "batch_38364", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-1297287476409344247", "sequence_length": 468, "latency": 0.12109923362731934, "success": true, "timestamp": 1748596762.4887242, "batch_id": "batch_38364", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "-1297287476409344247", "sequence_length": 465, "latency": 0.16132044792175293, "success": true, "timestamp": 1748596762.609847, "batch_id": "batch_38364", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "-1297287476409344247", "sequence_length": 466, "latency": 0.0660545825958252, "success": true, "timestamp": 1748596762.77119, "batch_id": "batch_38364", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "-8783201037438874235", "sequence_length": 540, "latency": 1.683760643005371, "success": true, "timestamp": 1748596762.8374, "batch_id": "batch_38364", "batch_index": 5, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-8783201037438874235", "sequence_length": 539, "latency": 1.6670732498168945, "success": true, "timestamp": 1748596764.521191, "batch_id": "batch_38364", "batch_index": 5, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-8783201037438874235", "sequence_length": 539, "latency": 1.6621334552764893, "success": true, "timestamp": 1748596766.188298, "batch_id": "batch_38364", "batch_index": 5, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-8783201037438874235", "sequence_length": 537, "latency": 1.6682202816009521, "success": true, "timestamp": 1748596767.8504589, "batch_id": "batch_38364", "batch_index": 5, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-8783201037438874235", "sequence_length": 539, "latency": 1.66823410987854, "success": true, "timestamp": 1748596769.518711, "batch_id": "batch_38364", "batch_index": 5, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-8783201037438874235", "sequence_length": 539, "latency": 1.6733741760253906, "success": true, "timestamp": 1748596771.1869798, "batch_id": "batch_38364", "batch_index": 5, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "-8783201037438874235", "sequence_length": 539, "latency": 1.6702609062194824, "success": true, "timestamp": 1748596772.8603876, "batch_id": "batch_38364", "batch_index": 5, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "-8783201037438874235", "sequence_length": 539, "latency": 1.6671342849731445, "success": true, "timestamp": 1748596774.530682, "batch_id": "batch_38364", "batch_index": 5, "processing_mode": "sequential"}]}