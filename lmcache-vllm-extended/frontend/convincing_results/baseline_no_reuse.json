{"experiment_config": {"num_batches": 5, "batch_size": 10, "repeat_ratio": 0.0, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null, "api_version": "v1", "scheduling": "client-side"}, "metrics": {"total_requests": 50, "successful_requests": 50, "failed_requests": 0, "average_latency": 0.7395992946624755, "total_latency": 36.97996473312378, "throughput": 1.352083496045465}, "detailed_results": [{"request_id": "req_1", "context_hash": "5649198978519573259", "sequence_length": 483, "latency": 0.5386662483215332, "success": true, "timestamp": 1748596114.21301, "batch_id": "batch_14208", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "1957183099835538873", "sequence_length": 12827, "latency": 2.87646484375, "success": true, "timestamp": 1748596114.7517169, "batch_id": "batch_14208", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-3695730493079222342", "sequence_length": 514, "latency": 0.10333681106567383, "success": true, "timestamp": 1748596117.6282215, "batch_id": "batch_14208", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-3583226411260602475", "sequence_length": 537, "latency": 1.6702663898468018, "success": true, "timestamp": 1748596117.7315896, "batch_id": "batch_14208", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-3269580954902217773", "sequence_length": 458, "latency": 0.43647241592407227, "success": true, "timestamp": 1748596119.401888, "batch_id": "batch_14208", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "8110066175113716691", "sequence_length": 507, "latency": 0.1701979637145996, "success": true, "timestamp": 1748596119.8383904, "batch_id": "batch_14208", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "910934355424832062", "sequence_length": 535, "latency": 0.3422508239746094, "success": true, "timestamp": 1748596120.0086162, "batch_id": "batch_14208", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "-3958317559172865373", "sequence_length": 555, "latency": 0.28098154067993164, "success": true, "timestamp": 1748596120.350898, "batch_id": "batch_14208", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "7561972568628307389", "sequence_length": 451, "latency": 0.4692494869232178, "success": true, "timestamp": 1748596120.6319115, "batch_id": "batch_14208", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "6691831389132785577", "sequence_length": 459, "latency": 0.6959896087646484, "success": true, "timestamp": 1748596121.1011903, "batch_id": "batch_14208", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "-3958317559172865373", "sequence_length": 556, "latency": 0.23713898658752441, "success": true, "timestamp": 1748596121.7973907, "batch_id": "batch_14209", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-3269580954902217773", "sequence_length": 456, "latency": 0.07054829597473145, "success": true, "timestamp": 1748596122.034557, "batch_id": "batch_14209", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "2368997441022424791", "sequence_length": 482, "latency": 0.08046650886535645, "success": true, "timestamp": 1748596122.1051292, "batch_id": "batch_14209", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "910934355424832062", "sequence_length": 535, "latency": 0.4025869369506836, "success": true, "timestamp": 1748596122.1856182, "batch_id": "batch_14209", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-74491953335549793", "sequence_length": 468, "latency": 1.1328802108764648, "success": true, "timestamp": 1748596122.5882292, "batch_id": "batch_14209", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "8110066175113716691", "sequence_length": 507, "latency": 0.14799284934997559, "success": true, "timestamp": 1748596123.7211397, "batch_id": "batch_14209", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "-3583226411260602475", "sequence_length": 539, "latency": 1.6716208457946777, "success": true, "timestamp": 1748596123.869162, "batch_id": "batch_14209", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "1957183099835538873", "sequence_length": 12831, "latency": 2.8554952144622803, "success": true, "timestamp": 1748596125.540813, "batch_id": "batch_14209", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "-75619702365379919", "sequence_length": 503, "latency": 0.07056593894958496, "success": true, "timestamp": 1748596128.396343, "batch_id": "batch_14209", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "7561972568628307389", "sequence_length": 448, "latency": 0.12563657760620117, "success": true, "timestamp": 1748596128.4669366, "batch_id": "batch_14209", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "1957183099835538873", "sequence_length": 12828, "latency": 2.7688724994659424, "success": true, "timestamp": 1748596128.5927649, "batch_id": "batch_14210", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-3695730493079222342", "sequence_length": 511, "latency": 0.20240187644958496, "success": true, "timestamp": 1748596131.3616705, "batch_id": "batch_14210", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-74491953335549793", "sequence_length": 469, "latency": 0.17432212829589844, "success": true, "timestamp": 1748596131.564102, "batch_id": "batch_14210", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-3269580954902217773", "sequence_length": 458, "latency": 0.08093714714050293, "success": true, "timestamp": 1748596131.7384577, "batch_id": "batch_14210", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-75619702365379919", "sequence_length": 506, "latency": 0.08410453796386719, "success": true, "timestamp": 1748596131.8194218, "batch_id": "batch_14210", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "910934355424832062", "sequence_length": 533, "latency": 0.4982430934906006, "success": true, "timestamp": 1748596131.9035525, "batch_id": "batch_14210", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "6859101049604714692", "sequence_length": 495, "latency": 0.08649230003356934, "success": true, "timestamp": 1748596132.4018266, "batch_id": "batch_14210", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "5649198978519573259", "sequence_length": 484, "latency": 0.06651616096496582, "success": true, "timestamp": 1748596132.488352, "batch_id": "batch_14210", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "2368997441022424791", "sequence_length": 482, "latency": 0.06575560569763184, "success": true, "timestamp": 1748596132.5548966, "batch_id": "batch_14210", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "6691831389132785577", "sequence_length": 462, "latency": 0.058641910552978516, "success": true, "timestamp": 1748596132.620676, "batch_id": "batch_14210", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "8110066175113716691", "sequence_length": 507, "latency": 1.672177791595459, "success": true, "timestamp": 1748596132.679481, "batch_id": "batch_14211", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-3695730493079222342", "sequence_length": 511, "latency": 0.44059276580810547, "success": true, "timestamp": 1748596134.3516884, "batch_id": "batch_14211", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-3269580954902217773", "sequence_length": 458, "latency": 0.08044934272766113, "success": true, "timestamp": 1748596134.792312, "batch_id": "batch_14211", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "2368997441022424791", "sequence_length": 480, "latency": 0.056012630462646484, "success": true, "timestamp": 1748596134.8727903, "batch_id": "batch_14211", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "6691831389132785577", "sequence_length": 461, "latency": 0.27863478660583496, "success": true, "timestamp": 1748596134.9288273, "batch_id": "batch_14211", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "910934355424832062", "sequence_length": 535, "latency": 0.49288272857666016, "success": true, "timestamp": 1748596135.2074888, "batch_id": "batch_14211", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "1957183099835538873", "sequence_length": 12830, "latency": 2.9075403213500977, "success": true, "timestamp": 1748596135.700401, "batch_id": "batch_14211", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "-3583226411260602475", "sequence_length": 536, "latency": 1.6848957538604736, "success": true, "timestamp": 1748596138.6079738, "batch_id": "batch_14211", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "-3958317559172865373", "sequence_length": 555, "latency": 1.6834416389465332, "success": true, "timestamp": 1748596140.2929041, "batch_id": "batch_14211", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "7561972568628307389", "sequence_length": 449, "latency": 0.33326125144958496, "success": true, "timestamp": 1748596141.9763794, "batch_id": "batch_14211", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "2368997441022424791", "sequence_length": 482, "latency": 0.08139443397521973, "success": true, "timestamp": 1748596142.3098133, "batch_id": "batch_14212", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-3695730493079222342", "sequence_length": 514, "latency": 0.1842188835144043, "success": true, "timestamp": 1748596142.3912365, "batch_id": "batch_14212", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-3583226411260602475", "sequence_length": 539, "latency": 1.6676547527313232, "success": true, "timestamp": 1748596142.5754874, "batch_id": "batch_14212", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "7561972568628307389", "sequence_length": 452, "latency": 0.27310967445373535, "success": true, "timestamp": 1748596144.2431746, "batch_id": "batch_14212", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "1957183099835538873", "sequence_length": 12831, "latency": 2.8173389434814453, "success": true, "timestamp": 1748596144.5163133, "batch_id": "batch_14212", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-3269580954902217773", "sequence_length": 459, "latency": 0.08442568778991699, "success": true, "timestamp": 1748596147.3336866, "batch_id": "batch_14212", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "8110066175113716691", "sequence_length": 507, "latency": 1.6854236125946045, "success": true, "timestamp": 1748596147.4181416, "batch_id": "batch_14212", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "1858173468210633081", "sequence_length": 545, "latency": 0.07575559616088867, "success": true, "timestamp": 1748596149.1035974, "batch_id": "batch_14212", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "910934355424832062", "sequence_length": 536, "latency": 0.4428744316101074, "success": true, "timestamp": 1748596149.1793792, "batch_id": "batch_14212", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "-3958317559172865373", "sequence_length": 556, "latency": 1.5727839469909668, "success": true, "timestamp": 1748596149.622279, "batch_id": "batch_14212", "batch_index": 4, "processing_mode": "sequential"}]}