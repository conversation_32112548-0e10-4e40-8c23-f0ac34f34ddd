{"experiment_config": {"num_batches": 2, "batch_size": 20, "repeat_ratio": 0.4, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null, "api_version": "v1", "scheduling": "client-side"}, "metrics": {"total_requests": 40, "successful_requests": 40, "failed_requests": 0, "average_latency": 1.2736473321914672, "total_latency": 50.94589328765869, "throughput": 0.7851467001303859}, "detailed_results": [{"request_id": "req_1", "context_hash": "-4048756973661552517", "sequence_length": 459, "latency": 1.710925817489624, "success": true, "timestamp": 1748597054.680996, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-4048756973661552517", "sequence_length": 461, "latency": 1.6492481231689453, "success": true, "timestamp": 1748597056.3919652, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "689253667396952244", "sequence_length": 465, "latency": 1.662635087966919, "success": true, "timestamp": 1748597058.041252, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-6188555007794869170", "sequence_length": 556, "latency": 0.806694746017456, "success": true, "timestamp": 1748597059.703928, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "8834411441617611625", "sequence_length": 458, "latency": 0.7244153022766113, "success": true, "timestamp": 1748597060.510658, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "3198664249128964932", "sequence_length": 12830, "latency": 2.8314974308013916, "success": true, "timestamp": 1748597061.2351072, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "3198664249128964932", "sequence_length": 12831, "latency": 1.775822639465332, "success": true, "timestamp": 1748597064.0666409, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "-304529695073529219", "sequence_length": 504, "latency": 1.6744413375854492, "success": true, "timestamp": 1748597065.842501, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "8834411441617611625", "sequence_length": 456, "latency": 0.5001122951507568, "success": true, "timestamp": 1748597067.5169768, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "-4048756973661552517", "sequence_length": 461, "latency": 0.8589987754821777, "success": true, "timestamp": 1748597068.0171213, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_11", "context_hash": "-4109984842806735658", "sequence_length": 537, "latency": 1.6774098873138428, "success": true, "timestamp": 1748597068.8761523, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_12", "context_hash": "6937849021012164337", "sequence_length": 483, "latency": 1.679321527481079, "success": true, "timestamp": 1748597070.5535977, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_13", "context_hash": "-304529695073529219", "sequence_length": 505, "latency": 1.6630377769470215, "success": true, "timestamp": 1748597072.2329538, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_14", "context_hash": "-304529695073529219", "sequence_length": 508, "latency": 1.661987066268921, "success": true, "timestamp": 1748597073.8960278, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_15", "context_hash": "-3231665085615864137", "sequence_length": 452, "latency": 0.6171185970306396, "success": true, "timestamp": 1748597075.5580485, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_16", "context_hash": "-4109984842806735658", "sequence_length": 539, "latency": 1.6654052734375, "success": true, "timestamp": 1748597076.175202, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_17", "context_hash": "-5493166108662168262", "sequence_length": 515, "latency": 0.2035975456237793, "success": true, "timestamp": 1748597077.8406415, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_18", "context_hash": "3451372683309701944", "sequence_length": 506, "latency": 0.06995582580566406, "success": true, "timestamp": 1748597078.0442703, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_19", "context_hash": "2941676011473255543", "sequence_length": 533, "latency": 0.5116405487060547, "success": true, "timestamp": 1748597078.1142597, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_20", "context_hash": "-3231665085615864137", "sequence_length": 452, "latency": 0.6826815605163574, "success": true, "timestamp": 1748597078.625934, "batch_id": "batch_54678", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "3451372683309701944", "sequence_length": 502, "latency": 1.666412353515625, "success": true, "timestamp": 1748597079.3088398, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-5493166108662168262", "sequence_length": 514, "latency": 0.11657452583312988, "success": true, "timestamp": 1748597080.9752872, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-3231665085615864137", "sequence_length": 449, "latency": 0.815960168838501, "success": true, "timestamp": 1748597081.0918899, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "689253667396952244", "sequence_length": 468, "latency": 0.3875086307525635, "success": true, "timestamp": 1748597081.9078841, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "233211757302099416", "sequence_length": 479, "latency": 0.6885833740234375, "success": true, "timestamp": 1748597082.2954216, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-4048756973661552517", "sequence_length": 459, "latency": 0.7650930881500244, "success": true, "timestamp": 1748597082.9840353, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "233211757302099416", "sequence_length": 482, "latency": 0.5290794372558594, "success": true, "timestamp": 1748597083.7491636, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "8834411441617611625", "sequence_length": 458, "latency": 1.6759748458862305, "success": true, "timestamp": 1748597084.2782733, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "233211757302099416", "sequence_length": 482, "latency": 1.4612128734588623, "success": true, "timestamp": 1748597085.9542823, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "-6188555007794869170", "sequence_length": 552, "latency": 1.6922423839569092, "success": true, "timestamp": 1748597087.4155293, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_11", "context_hash": "-3231665085615864137", "sequence_length": 449, "latency": 1.6623563766479492, "success": true, "timestamp": 1748597089.1078057, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_12", "context_hash": "-3231665085615864137", "sequence_length": 452, "latency": 1.6782658100128174, "success": true, "timestamp": 1748597090.7701967, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_13", "context_hash": "-5055244022447102773", "sequence_length": 495, "latency": 0.2765944004058838, "success": true, "timestamp": 1748597092.4484959, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_14", "context_hash": "-6188555007794869170", "sequence_length": 553, "latency": 1.6777384281158447, "success": true, "timestamp": 1748597092.725122, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_15", "context_hash": "3198664249128964932", "sequence_length": 12830, "latency": 2.7720208168029785, "success": true, "timestamp": 1748597094.402893, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_16", "context_hash": "2941676011473255543", "sequence_length": 533, "latency": 1.6915414333343506, "success": true, "timestamp": 1748597097.1749494, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_17", "context_hash": "-4109984842806735658", "sequence_length": 536, "latency": 1.699742078781128, "success": true, "timestamp": 1748597098.866523, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_18", "context_hash": "3451372683309701944", "sequence_length": 505, "latency": 1.684910535812378, "success": true, "timestamp": 1748597100.566298, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_19", "context_hash": "-4109984842806735658", "sequence_length": 537, "latency": 1.6797916889190674, "success": true, "timestamp": 1748597102.251243, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_20", "context_hash": "-5493166108662168262", "sequence_length": 511, "latency": 1.697342872619629, "success": true, "timestamp": 1748597103.9310682, "batch_id": "batch_54679", "batch_index": 1, "processing_mode": "sequential"}]}