{"experiment_config": {"num_batches": 4, "batch_size": 5, "repeat_ratio": 0.4, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null, "api_version": "v1", "scheduling": "client-side"}, "metrics": {"total_requests": 20, "successful_requests": 20, "failed_requests": 0, "average_latency": 0.4517495512962341, "total_latency": 9.034991025924683, "throughput": 2.213615923094191}, "detailed_results": [{"request_id": "req_1", "context_hash": "886012447900438072", "sequence_length": 552, "latency": 0.3425157070159912, "success": true, "timestamp": 1748595511.1216662, "batch_id": "batch_11119", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "886012447900438072", "sequence_length": 556, "latency": 0.19986391067504883, "success": true, "timestamp": 1748595511.4642189, "batch_id": "batch_11119", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-897875582873432462", "sequence_length": 12830, "latency": 2.8887548446655273, "success": true, "timestamp": 1748595511.664114, "batch_id": "batch_11119", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-3625155037688780498", "sequence_length": 507, "latency": 0.17398858070373535, "success": true, "timestamp": 1748595514.5529063, "batch_id": "batch_11119", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-897875582873432462", "sequence_length": 12827, "latency": 2.787409782409668, "success": true, "timestamp": 1748595514.7269268, "batch_id": "batch_11119", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "-7444225508825036378", "sequence_length": 502, "latency": 0.1152944564819336, "success": true, "timestamp": 1748595517.51456, "batch_id": "batch_11121", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-3625155037688780498", "sequence_length": 507, "latency": 0.1340477466583252, "success": true, "timestamp": 1748595517.6298847, "batch_id": "batch_11121", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-3625155037688780498", "sequence_length": 505, "latency": 0.22452402114868164, "success": true, "timestamp": 1748595517.763962, "batch_id": "batch_11121", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-3625155037688780498", "sequence_length": 507, "latency": 0.16524457931518555, "success": true, "timestamp": 1748595517.9885306, "batch_id": "batch_11121", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "3822648131553340959", "sequence_length": 483, "latency": 0.07985615730285645, "success": true, "timestamp": 1748595518.1538105, "batch_id": "batch_11121", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "8993771699416103877", "sequence_length": 511, "latency": 0.45757603645324707, "success": true, "timestamp": 1748595518.2338588, "batch_id": "batch_11121", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-3753412792396113852", "sequence_length": 462, "latency": 0.08024883270263672, "success": true, "timestamp": 1748595518.6914647, "batch_id": "batch_11121", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-3753412792396113852", "sequence_length": 461, "latency": 0.06868171691894531, "success": true, "timestamp": 1748595518.7717426, "batch_id": "batch_11121", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "8993771699416103877", "sequence_length": 514, "latency": 0.420743465423584, "success": true, "timestamp": 1748595518.8404515, "batch_id": "batch_11121", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "6275572223193734235", "sequence_length": 483, "latency": 0.5079116821289062, "success": true, "timestamp": 1748595519.2612216, "batch_id": "batch_11121", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "-5526508957711504690", "sequence_length": 466, "latency": 0.12791848182678223, "success": true, "timestamp": 1748595519.7692947, "batch_id": "batch_11121", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-3753412792396113852", "sequence_length": 461, "latency": 0.0694875717163086, "success": true, "timestamp": 1748595519.8972416, "batch_id": "batch_11121", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "8993771699416103877", "sequence_length": 515, "latency": 0.06745076179504395, "success": true, "timestamp": 1748595519.9667559, "batch_id": "batch_11121", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-3753412792396113852", "sequence_length": 461, "latency": 0.059803009033203125, "success": true, "timestamp": 1748595520.034232, "batch_id": "batch_11121", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-3753412792396113852", "sequence_length": 461, "latency": 0.06366968154907227, "success": true, "timestamp": 1748595520.0940554, "batch_id": "batch_11121", "batch_index": 3, "processing_mode": "sequential"}]}