{"experiment_config": {"num_batches": 3, "batch_size": 8, "repeat_ratio": 0.4, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null, "api_version": "v1", "scheduling": "client-side"}, "metrics": {"total_requests": 24, "successful_requests": 24, "failed_requests": 0, "average_latency": 0.7366099158922831, "total_latency": 17.678637981414795, "throughput": 1.3575706468581306}, "detailed_results": [{"request_id": "req_1", "context_hash": "-6827109015100895267", "sequence_length": 553, "latency": 0.34287071228027344, "success": true, "timestamp": 1748595382.3062644, "batch_id": "batch_82305", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-6827109015100895267", "sequence_length": 552, "latency": 0.2542541027069092, "success": true, "timestamp": 1748595382.6491714, "batch_id": "batch_82305", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-1018237895962676153", "sequence_length": 449, "latency": 0.6638710498809814, "success": true, "timestamp": 1748595382.9034572, "batch_id": "batch_82305", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-1018237895962676153", "sequence_length": 451, "latency": 0.25536131858825684, "success": true, "timestamp": 1748595383.5673604, "batch_id": "batch_82305", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-4809891230446170815", "sequence_length": 466, "latency": 0.43747758865356445, "success": true, "timestamp": 1748595383.8227487, "batch_id": "batch_82305", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-1018237895962676153", "sequence_length": 452, "latency": 1.647918939590454, "success": true, "timestamp": 1748595384.2602546, "batch_id": "batch_82305", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "-5884086795509360636", "sequence_length": 508, "latency": 0.13793396949768066, "success": true, "timestamp": 1748595385.9082055, "batch_id": "batch_82305", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "5619547809131702083", "sequence_length": 459, "latency": 1.2235870361328125, "success": true, "timestamp": 1748595386.0461743, "batch_id": "batch_82305", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "5619547809131702083", "sequence_length": 455, "latency": 1.3757004737854004, "success": true, "timestamp": 1748595387.2699904, "batch_id": "batch_82305", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-4886272087133627692", "sequence_length": 539, "latency": 1.6737890243530273, "success": true, "timestamp": 1748595388.645725, "batch_id": "batch_82305", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "8876762494304961209", "sequence_length": 495, "latency": 1.6715738773345947, "success": true, "timestamp": 1748595390.3195448, "batch_id": "batch_82305", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "5619547809131702083", "sequence_length": 458, "latency": 1.0502099990844727, "success": true, "timestamp": 1748595391.9911478, "batch_id": "batch_82305", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "7388428308438488292", "sequence_length": 514, "latency": 0.07598137855529785, "success": true, "timestamp": 1748595393.0413861, "batch_id": "batch_82305", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "7388428308438488292", "sequence_length": 511, "latency": 0.4867820739746094, "success": true, "timestamp": 1748595393.1173985, "batch_id": "batch_82305", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "7297077604521695050", "sequence_length": 480, "latency": 0.06603455543518066, "success": true, "timestamp": 1748595393.604209, "batch_id": "batch_82305", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "8876762494304961209", "sequence_length": 495, "latency": 0.5355126857757568, "success": true, "timestamp": 1748595393.670267, "batch_id": "batch_82305", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "-4886272087133627692", "sequence_length": 536, "latency": 0.5598845481872559, "success": true, "timestamp": 1748595394.2059605, "batch_id": "batch_82305", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-1018237895962676153", "sequence_length": 451, "latency": 0.477358341217041, "success": true, "timestamp": 1748595394.7658734, "batch_id": "batch_82305", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "5619547809131702083", "sequence_length": 455, "latency": 0.06397604942321777, "success": true, "timestamp": 1748595395.2432618, "batch_id": "batch_82305", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-3566550857625009971", "sequence_length": 484, "latency": 0.47280097007751465, "success": true, "timestamp": 1748595395.3072655, "batch_id": "batch_82305", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-3566550857625009971", "sequence_length": 487, "latency": 1.6546239852905273, "success": true, "timestamp": 1748595395.780097, "batch_id": "batch_82305", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-6827109015100895267", "sequence_length": 556, "latency": 1.6787011623382568, "success": true, "timestamp": 1748595397.4347508, "batch_id": "batch_82305", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "-3566550857625009971", "sequence_length": 486, "latency": 0.4272143840789795, "success": true, "timestamp": 1748595399.1134858, "batch_id": "batch_82305", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "-3566550857625009971", "sequence_length": 483, "latency": 0.4452197551727295, "success": true, "timestamp": 1748595399.540734, "batch_id": "batch_82305", "batch_index": 2, "processing_mode": "sequential"}]}