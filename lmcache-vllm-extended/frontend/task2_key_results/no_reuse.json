{"experiment_config": {"num_batches": 3, "batch_size": 8, "repeat_ratio": 0.0, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null, "api_version": "v1", "scheduling": "client-side"}, "metrics": {"total_requests": 24, "successful_requests": 24, "failed_requests": 0, "average_latency": 0.7542925775051117, "total_latency": 18.10302186012268, "throughput": 1.3257455128454094}, "detailed_results": [{"request_id": "req_1", "context_hash": "-7192806776672646642", "sequence_length": 542, "latency": 0.07668852806091309, "success": true, "timestamp": 1748595353.964704, "batch_id": "batch_53963", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "4600537252164460831", "sequence_length": 484, "latency": 0.08686971664428711, "success": true, "timestamp": 1748595354.0414288, "batch_id": "batch_53963", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-2403563284472842277", "sequence_length": 502, "latency": 0.07903671264648438, "success": true, "timestamp": 1748595354.1283333, "batch_id": "batch_53963", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-6798974182818499830", "sequence_length": 480, "latency": 1.1605491638183594, "success": true, "timestamp": 1748595354.2073998, "batch_id": "batch_53963", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "4402908415409145290", "sequence_length": 536, "latency": 0.33439064025878906, "success": true, "timestamp": 1748595355.3679838, "batch_id": "batch_53963", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-6344322262606444743", "sequence_length": 492, "latency": 0.10390400886535645, "success": true, "timestamp": 1748595355.702406, "batch_id": "batch_53963", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "-4877487476174346615", "sequence_length": 553, "latency": 0.2611055374145508, "success": true, "timestamp": 1748595355.806341, "batch_id": "batch_53963", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "-4034095259807226363", "sequence_length": 459, "latency": 1.54032301902771, "success": true, "timestamp": 1748595356.06748, "batch_id": "batch_53963", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "940758366223978207", "sequence_length": 468, "latency": 1.4026012420654297, "success": true, "timestamp": 1748595357.6080275, "batch_id": "batch_53963", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "7920023711074045496", "sequence_length": 507, "latency": 0.10721802711486816, "success": true, "timestamp": 1748595359.0106587, "batch_id": "batch_53963", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-2403563284472842277", "sequence_length": 506, "latency": 0.05408477783203125, "success": true, "timestamp": 1748595359.117904, "batch_id": "batch_53963", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "8437302670975990445", "sequence_length": 448, "latency": 1.4351375102996826, "success": true, "timestamp": 1748595359.172017, "batch_id": "batch_53963", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "4402908415409145290", "sequence_length": 532, "latency": 0.7160050868988037, "success": true, "timestamp": 1748595360.6071875, "batch_id": "batch_53963", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-6798974182818499830", "sequence_length": 479, "latency": 0.8340396881103516, "success": true, "timestamp": 1748595361.3232217, "batch_id": "batch_53963", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "1025768004198409737", "sequence_length": 512, "latency": 0.8168632984161377, "success": true, "timestamp": 1748595362.1572897, "batch_id": "batch_53963", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "-4877487476174346615", "sequence_length": 555, "latency": 0.2529919147491455, "success": true, "timestamp": 1748595362.974181, "batch_id": "batch_53963", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "940758366223978207", "sequence_length": 469, "latency": 0.38208651542663574, "success": true, "timestamp": 1748595363.2273643, "batch_id": "batch_53963", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "5300862155054245482", "sequence_length": 12828, "latency": 2.884065628051758, "success": true, "timestamp": 1748595363.6094775, "batch_id": "batch_53963", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-6328462277182062635", "sequence_length": 539, "latency": 0.530707597732544, "success": true, "timestamp": 1748595366.4935768, "batch_id": "batch_53963", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-4034095259807226363", "sequence_length": 458, "latency": 1.6581501960754395, "success": true, "timestamp": 1748595367.0243144, "batch_id": "batch_53963", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "4600537252164460831", "sequence_length": 486, "latency": 0.4645957946777344, "success": true, "timestamp": 1748595368.6824996, "batch_id": "batch_53963", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "4402908415409145290", "sequence_length": 535, "latency": 1.1013154983520508, "success": true, "timestamp": 1748595369.1471252, "batch_id": "batch_53963", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "8437302670975990445", "sequence_length": 451, "latency": 1.662973165512085, "success": true, "timestamp": 1748595370.2484744, "batch_id": "batch_53963", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "7920023711074045496", "sequence_length": 507, "latency": 0.1573185920715332, "success": true, "timestamp": 1748595371.9114842, "batch_id": "batch_53963", "batch_index": 2, "processing_mode": "sequential"}]}