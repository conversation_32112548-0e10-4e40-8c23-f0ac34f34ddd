{"experiment_config": {"num_batches": 3, "batch_size": 8, "repeat_ratio": 0.8, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null, "api_version": "v1", "scheduling": "client-side"}, "metrics": {"total_requests": 24, "successful_requests": 24, "failed_requests": 0, "average_latency": 0.5514019628365835, "total_latency": 13.233647108078003, "throughput": 1.8135590139282212}, "detailed_results": [{"request_id": "req_1", "context_hash": "924126839403192060", "sequence_length": 537, "latency": 1.7124285697937012, "success": true, "timestamp": 1748595489.4464715, "batch_id": "batch_89445", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-8634918058392610851", "sequence_length": 449, "latency": 0.2972118854522705, "success": true, "timestamp": 1748595491.1589394, "batch_id": "batch_89445", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "924126839403192060", "sequence_length": 536, "latency": 1.6504426002502441, "success": true, "timestamp": 1748595491.4561844, "batch_id": "batch_89445", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "924126839403192060", "sequence_length": 539, "latency": 1.657109022140503, "success": true, "timestamp": 1748595493.106663, "batch_id": "batch_89445", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "924126839403192060", "sequence_length": 537, "latency": 1.6497654914855957, "success": true, "timestamp": 1748595494.7638013, "batch_id": "batch_89445", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-8634918058392610851", "sequence_length": 449, "latency": 0.06309795379638672, "success": true, "timestamp": 1748595496.4135985, "batch_id": "batch_89445", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "-8634918058392610851", "sequence_length": 452, "latency": 0.05520892143249512, "success": true, "timestamp": 1748595496.476725, "batch_id": "batch_89445", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "-8634918058392610851", "sequence_length": 449, "latency": 1.4363887310028076, "success": true, "timestamp": 1748595496.531963, "batch_id": "batch_89445", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "467096135519885724", "sequence_length": 505, "latency": 0.7793011665344238, "success": true, "timestamp": 1748595497.9685683, "batch_id": "batch_89445", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "467096135519885724", "sequence_length": 502, "latency": 0.07182097434997559, "success": true, "timestamp": 1748595498.7479002, "batch_id": "batch_89445", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "467096135519885724", "sequence_length": 506, "latency": 0.06924152374267578, "success": true, "timestamp": 1748595498.819748, "batch_id": "batch_89445", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "7370590372520758495", "sequence_length": 483, "latency": 0.4367828369140625, "success": true, "timestamp": 1748595498.8890142, "batch_id": "batch_89445", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "7370590372520758495", "sequence_length": 484, "latency": 0.4705805778503418, "success": true, "timestamp": 1748595499.3258207, "batch_id": "batch_89445", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "7370590372520758495", "sequence_length": 484, "latency": 0.4746410846710205, "success": true, "timestamp": 1748595499.7964292, "batch_id": "batch_89445", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "7370590372520758495", "sequence_length": 487, "latency": 0.4218907356262207, "success": true, "timestamp": 1748595500.2710977, "batch_id": "batch_89445", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "467096135519885724", "sequence_length": 506, "latency": 0.06612944602966309, "success": true, "timestamp": 1748595500.6930153, "batch_id": "batch_89445", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "244253044067069026", "sequence_length": 483, "latency": 0.06878042221069336, "success": true, "timestamp": 1748595500.7593307, "batch_id": "batch_89446", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "244253044067069026", "sequence_length": 482, "latency": 0.7338037490844727, "success": true, "timestamp": 1748595500.828143, "batch_id": "batch_89446", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "244253044067069026", "sequence_length": 482, "latency": 0.7460422515869141, "success": true, "timestamp": 1748595501.5619779, "batch_id": "batch_89446", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "244253044067069026", "sequence_length": 480, "latency": 0.07340550422668457, "success": true, "timestamp": 1748595502.3080475, "batch_id": "batch_89446", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-7697184493691330016", "sequence_length": 459, "latency": 0.0775899887084961, "success": true, "timestamp": 1748595502.3814757, "batch_id": "batch_89446", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "244253044067069026", "sequence_length": 483, "latency": 0.048293352127075195, "success": true, "timestamp": 1748595502.45909, "batch_id": "batch_89446", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "-7697184493691330016", "sequence_length": 455, "latency": 0.12730693817138672, "success": true, "timestamp": 1748595502.507404, "batch_id": "batch_89446", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "244253044067069026", "sequence_length": 482, "latency": 0.04638338088989258, "success": true, "timestamp": 1748595502.6347396, "batch_id": "batch_89446", "batch_index": 2, "processing_mode": "sequential"}]}