{"experiment_config": {"num_batches": 2, "batch_size": 15, "repeat_ratio": 0.4, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null, "api_version": "v1", "scheduling": "client-side"}, "metrics": {"total_requests": 30, "successful_requests": 30, "failed_requests": 0, "average_latency": 0.28371338844299315, "total_latency": 8.511401653289795, "throughput": 3.5246838560843283}, "detailed_results": [{"request_id": "req_1", "context_hash": "8485646800388557573", "sequence_length": 536, "latency": 0.7547991275787354, "success": true, "timestamp": 1748595529.9808865, "batch_id": "batch_29979", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "8485646800388557573", "sequence_length": 532, "latency": 0.3777623176574707, "success": true, "timestamp": 1748595530.7357244, "batch_id": "batch_29979", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "7463665885471459809", "sequence_length": 459, "latency": 0.3964719772338867, "success": true, "timestamp": 1748595531.1135263, "batch_id": "batch_29979", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "1656984172522236095", "sequence_length": 486, "latency": 0.4562039375305176, "success": true, "timestamp": 1748595531.5100331, "batch_id": "batch_29979", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "2772599470571898513", "sequence_length": 482, "latency": 1.1103811264038086, "success": true, "timestamp": 1748595531.9662697, "batch_id": "batch_29979", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-1032312020365418755", "sequence_length": 506, "latency": 0.09532976150512695, "success": true, "timestamp": 1748595533.0766807, "batch_id": "batch_29979", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "7463665885471459809", "sequence_length": 461, "latency": 0.06440997123718262, "success": true, "timestamp": 1748595533.1720412, "batch_id": "batch_29979", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "3111292318032679271", "sequence_length": 504, "latency": 0.21649909019470215, "success": true, "timestamp": 1748595533.2364798, "batch_id": "batch_29979", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "4970665729414124316", "sequence_length": 469, "latency": 0.22540593147277832, "success": true, "timestamp": 1748595533.4530056, "batch_id": "batch_29979", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "7463665885471459809", "sequence_length": 461, "latency": 0.06457400321960449, "success": true, "timestamp": 1748595533.6784518, "batch_id": "batch_29979", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_11", "context_hash": "1656984172522236095", "sequence_length": 487, "latency": 0.4419524669647217, "success": true, "timestamp": 1748595533.7430594, "batch_id": "batch_29979", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_12", "context_hash": "3111292318032679271", "sequence_length": 507, "latency": 0.16368746757507324, "success": true, "timestamp": 1748595534.1850417, "batch_id": "batch_29979", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_13", "context_hash": "5870503227152853667", "sequence_length": 546, "latency": 0.1413431167602539, "success": true, "timestamp": 1748595534.3487568, "batch_id": "batch_29979", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_14", "context_hash": "5870503227152853667", "sequence_length": 546, "latency": 0.10114502906799316, "success": true, "timestamp": 1748595534.4901268, "batch_id": "batch_29979", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_15", "context_hash": "-2341685550441552541", "sequence_length": 555, "latency": 0.2692596912384033, "success": true, "timestamp": 1748595534.5912945, "batch_id": "batch_29979", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "3111292318032679271", "sequence_length": 507, "latency": 0.17578649520874023, "success": true, "timestamp": 1748595534.8607817, "batch_id": "batch_29980", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "3111292318032679271", "sequence_length": 505, "latency": 0.2176523208618164, "success": true, "timestamp": 1748595535.0365953, "batch_id": "batch_29980", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "1656984172522236095", "sequence_length": 486, "latency": 0.4296705722808838, "success": true, "timestamp": 1748595535.2542741, "batch_id": "batch_29980", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "8485646800388557573", "sequence_length": 536, "latency": 0.6963942050933838, "success": true, "timestamp": 1748595535.6839714, "batch_id": "batch_29980", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-3557092307820379279", "sequence_length": 458, "latency": 0.11360597610473633, "success": true, "timestamp": 1748595536.3803978, "batch_id": "batch_29980", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "4970665729414124316", "sequence_length": 468, "latency": 0.15951228141784668, "success": true, "timestamp": 1748595536.4940345, "batch_id": "batch_29980", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "8485646800388557573", "sequence_length": 536, "latency": 0.32424187660217285, "success": true, "timestamp": 1748595536.6535778, "batch_id": "batch_29980", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "7463665885471459809", "sequence_length": 462, "latency": 0.08006906509399414, "success": true, "timestamp": 1748595536.9778426, "batch_id": "batch_29980", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "8485646800388557573", "sequence_length": 532, "latency": 0.39070796966552734, "success": true, "timestamp": 1748595537.0579438, "batch_id": "batch_29980", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "179782395070351383", "sequence_length": 511, "latency": 0.12236261367797852, "success": true, "timestamp": 1748595537.4486792, "batch_id": "batch_29980", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_11", "context_hash": "5870503227152853667", "sequence_length": 546, "latency": 0.13223552703857422, "success": true, "timestamp": 1748595537.5710704, "batch_id": "batch_29980", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_12", "context_hash": "4970665729414124316", "sequence_length": 465, "latency": 0.24988508224487305, "success": true, "timestamp": 1748595537.7033346, "batch_id": "batch_29980", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_13", "context_hash": "-3557092307820379279", "sequence_length": 455, "latency": 0.051137447357177734, "success": true, "timestamp": 1748595537.9532478, "batch_id": "batch_29980", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_14", "context_hash": "8485646800388557573", "sequence_length": 535, "latency": 0.32822132110595703, "success": true, "timestamp": 1748595538.004412, "batch_id": "batch_29980", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_15", "context_hash": "3181950120239689628", "sequence_length": 448, "latency": 0.16069388389587402, "success": true, "timestamp": 1748595538.3326602, "batch_id": "batch_29980", "batch_index": 1, "processing_mode": "sequential"}]}