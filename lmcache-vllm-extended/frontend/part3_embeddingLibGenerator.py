# context embeddings generation script

from transformers import AutoModel, AutoTokenizer
import torch
import numpy as np

import os
import numpy as np
import pandas as pd
from typing import Dict
from transformers import AutoTokenizer
MODEL_NAME = "Qwen/Qwen2.5-1.5B"

def read_chunks(file_folder) -> Dict[str, str]:
    """
    Read all the txt files in the folder and return the filenames
    """
    filenames = os.listdir(file_folder)
    ret = {}
    for filename in filenames:
        if not filename.endswith("txt"):
            continue
        key = filename.removesuffix(".txt")
        with open(os.path.join(file_folder, filename), "r") as fin:
            value = fin.read()
        ret[key] = value

    return ret

def get_embedding(text: str) -> np.ndarray:
    inputs = tokenizer(text, return_tensors="pt", truncation=True, max_length=512).to(device)
    with torch.no_grad():
        outputs = model(**inputs, output_hidden_states=True)
        hidden_states = outputs.hidden_states[-1]  # last layer
        sentence_embedding = hidden_states.mean(dim=1).squeeze().cpu().numpy()  # [hidden_size]
    return sentence_embedding

chunks = read_chunks("data/")

device = torch.device("cpu")
tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
model = AutoModel.from_pretrained(MODEL_NAME).to(device)

embedded_chunks = {}
for key in chunks:
    text = chunks[key]
    embedding = get_embedding(text)
    embedded_chunks[key] = embedding
    

df = pd.DataFrame.from_dict(embedded_chunks, orient='index')
df.to_parquet("embedded_chunks.parquet")
