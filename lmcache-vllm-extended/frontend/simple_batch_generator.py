#!/usr/bin/env python3
"""
Simplified Batch Request Generator for Task 2
Frontend only handles batch generation and request sending.
Scheduling logic is now implemented in the LLM server (custom_api.py).
"""

import asyncio
import aiohttp
import json
import time
import os
import random
from dataclasses import dataclass
from typing import List, Dict, Any
import numpy as np


@dataclass
class Request:
    """Represents a single request."""
    id: str
    context: str
    question: str
    context_hash: str
    sequence_length: int


@dataclass
class Batch:
    """Represents a batch of requests."""
    id: str
    requests: List[Request]


class BatchRequestGenerator:
    """Generates batches of requests for testing."""

    def load_contexts(self, data_dir: str) -> List[str]:
        """Load context data from text files."""
        contexts = []
        data_path = os.path.join(os.path.dirname(__file__), data_dir)

        if not os.path.exists(data_path):
            print(f"Warning: Data directory {data_path} not found")
            return []

        for filename in os.listdir(data_path):
            if filename.endswith('.txt'):
                filepath = os.path.join(data_path, filename)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content:
                            contexts.append(content)
                except Exception as e:
                    print(f"Error reading {filepath}: {e}")

        print(f"Loaded {len(contexts)} contexts from {data_path}")
        return contexts

    def generate_batch(self, contexts: List[str], questions: List[str],
                      batch_size: int, repeat_ratio: float = 0.3,
                      diversity_level: float = 1.0) -> Batch:
        """Generate a single batch of requests."""

        # Determine how many unique contexts to use based on diversity
        max_unique_contexts = min(len(contexts), max(1, int(batch_size * diversity_level)))

        # Select contexts for this batch
        if repeat_ratio > 0:
            # Calculate how many requests should reuse contexts
            repeat_count = int(batch_size * repeat_ratio)
            unique_count = batch_size - repeat_count

            # Ensure we don't exceed available contexts
            unique_count = min(unique_count, max_unique_contexts)

            # Select unique contexts
            selected_contexts = random.sample(contexts, unique_count)

            # Add repeated contexts
            batch_contexts = selected_contexts.copy()
            for _ in range(repeat_count):
                batch_contexts.append(random.choice(selected_contexts))

            # Shuffle to mix repeated and unique contexts
            random.shuffle(batch_contexts)
        else:
            # No repetition, just select unique contexts
            num_contexts = min(batch_size, max_unique_contexts)
            batch_contexts = random.sample(contexts, num_contexts)

            # If we need more requests than unique contexts, repeat some
            while len(batch_contexts) < batch_size:
                batch_contexts.append(random.choice(batch_contexts))

        # Generate requests
        requests = []
        batch_id = f"batch_{int(time.time() * 1000) % 100000}"

        for i in range(batch_size):
            context = batch_contexts[i % len(batch_contexts)]
            question = random.choice(questions)

            # Calculate sequence length (context + question + some overhead)
            sequence_length = len(context.split()) + len(question.split()) + 50

            # Generate context hash
            context_hash = str(hash(context))

            request = Request(
                id=f"req_{i+1}",
                context=context,
                question=question,
                context_hash=context_hash,
                sequence_length=sequence_length
            )
            requests.append(request)

        return Batch(id=batch_id, requests=requests)

    def generate_multiple_batches(self, contexts: List[str], questions: List[str],
                                 num_batches: int, batch_size: int,
                                 repeat_ratio: float = 0.3,
                                 diversity_level: float = 1.0) -> List[Batch]:
        """Generate multiple batches."""
        batches = []

        for i in range(num_batches):
            batch = self.generate_batch(
                contexts=contexts,
                questions=questions,
                batch_size=batch_size,
                repeat_ratio=repeat_ratio,
                diversity_level=diversity_level
            )
            batches.append(batch)

        return batches


class BatchProcessor:
    """Processes batches of requests and measures performance."""

    def __init__(self, server_ip: str, server_port: int, use_v2_api: bool = True):
        self.server_ip = server_ip
        self.server_port = server_port
        # Use v2 API which has the smart scheduler
        self.base_url = f"http://{server_ip}:{server_port}/v2" if use_v2_api else f"http://{server_ip}:{server_port}/v1"

    async def process_request(self, session: aiohttp.ClientSession, request: Request) -> Dict[str, Any]:
        """Process a single request and measure latency."""
        start_time = time.time()

        # Prepare the request payload
        payload = {
            "model": "Qwen/Qwen2.5-1.5B-Instruct",
            "messages": [
                {"role": "system", "content": request.context},
                {"role": "user", "content": request.question}
            ],
            "temperature": 0.5,
            "max_tokens": 100,
            "stream": False
        }

        try:
            async with session.post(f"{self.base_url}/chat/completions", json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    end_time = time.time()
                    latency = end_time - start_time

                    return {
                        "request_id": request.id,
                        "context_hash": request.context_hash,
                        "sequence_length": request.sequence_length,
                        "latency": latency,
                        "success": True,
                        "timestamp": start_time
                    }
                else:
                    return {
                        "request_id": request.id,
                        "context_hash": request.context_hash,
                        "sequence_length": request.sequence_length,
                        "latency": None,
                        "success": False,
                        "error": f"HTTP {response.status}",
                        "timestamp": start_time
                    }
        except Exception as e:
            end_time = time.time()
            return {
                "request_id": request.id,
                "context_hash": request.context_hash,
                "sequence_length": request.sequence_length,
                "latency": None,
                "success": False,
                "error": str(e),
                "timestamp": start_time
            }

    async def process_batch_sequential(self, requests: List[Request]) -> List[Dict[str, Any]]:
        """Process requests sequentially (one after another)."""
        results = []

        async with aiohttp.ClientSession() as session:
            for request in requests:
                result = await self.process_request(session, request)
                results.append(result)
                print(f"Processed {request.id}: {result['latency']:.3f}s" if result['success'] else f"Failed {request.id}")

        return results

    async def process_batch_parallel(self, requests: List[Request], max_concurrent: int = 5) -> List[Dict[str, Any]]:
        """Process requests in parallel with limited concurrency."""
        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_with_semaphore(session: aiohttp.ClientSession, request: Request):
            async with semaphore:
                return await self.process_request(session, request)

        async with aiohttp.ClientSession() as session:
            tasks = [process_with_semaphore(session, request) for request in requests]
            results = await asyncio.gather(*tasks)

        return results


def main():
    import argparse

    parser = argparse.ArgumentParser(description='Simple Batch Request Generator')
    parser.add_argument("--ip", required=True, help="Server IP address")
    parser.add_argument("--port", type=int, required=True, help="Server port")
    parser.add_argument("--num_batches", type=int, default=5, help="Number of batches to generate")
    parser.add_argument("--batch_size", type=int, default=10, help="Size of each batch")
    parser.add_argument("--repeat_ratio", type=float, default=0.3, help="Ratio of context reuse")
    parser.add_argument("--diversity", type=float, default=1.0, help="Context diversity level")
    parser.add_argument("--output", required=True, help="Output file for results")
    parser.add_argument("--processing_mode", choices=["sequential", "parallel"], default="sequential",
                        help="Request processing mode")
    parser.add_argument("--max_concurrent", type=int, default=5, help="Max concurrent requests for parallel mode")
    parser.add_argument("--use_v1_api", action="store_true", help="Use v1 API (no scheduling) instead of v2")

    args = parser.parse_args()

    # Initialize components
    generator = BatchRequestGenerator()
    processor = BatchProcessor(args.ip, args.port, use_v2_api=not args.use_v1_api)

    # Load contexts and questions
    contexts = generator.load_contexts("data")
    questions = [
        "What is the main topic of this document?",
        "Summarize the content briefly.",
        "Explain the main concept in section 2.",
        "What are the key findings?",
        "How does this relate to current research?"
    ]

    print(f"Generating {args.num_batches} batches of size {args.batch_size}")
    print(f"Using {'v1' if args.use_v1_api else 'v2'} API ({'no scheduling' if args.use_v1_api else 'with smart scheduling'})")

    # Generate batches
    batches = generator.generate_multiple_batches(
        contexts=contexts,
        questions=questions,
        num_batches=args.num_batches,
        batch_size=args.batch_size,
        repeat_ratio=args.repeat_ratio,
        diversity_level=args.diversity
    )

    # Process batches
    all_results = []

    async def process_all_batches():
        for i, batch in enumerate(batches):
            print(f"\nProcessing batch {i+1}/{len(batches)} (ID: {batch.id})")

            # Process the requests (scheduling happens on server side now)
            if args.processing_mode == "sequential":
                results = await processor.process_batch_sequential(batch.requests)
            else:
                results = await processor.process_batch_parallel(batch.requests, args.max_concurrent)

            # Add batch metadata to results
            for result in results:
                result["batch_id"] = batch.id
                result["batch_index"] = i
                result["processing_mode"] = args.processing_mode

            all_results.extend(results)

    # Run the async processing
    asyncio.run(process_all_batches())

    # Calculate metrics
    successful_results = [r for r in all_results if r['success']]
    total_latency = sum(r['latency'] for r in successful_results)
    avg_latency = total_latency / len(successful_results) if successful_results else 0
    throughput = len(successful_results) / total_latency if total_latency > 0 else 0

    # Compile final results
    final_results = {
        "experiment_config": {
            "num_batches": args.num_batches,
            "batch_size": args.batch_size,
            "repeat_ratio": args.repeat_ratio,
            "diversity_level": args.diversity,
            "processing_mode": args.processing_mode,
            "max_concurrent": args.max_concurrent if args.processing_mode == "parallel" else None,
            "api_version": "v1" if args.use_v1_api else "v2",
            "scheduling": "client-side" if args.use_v1_api else "server-side"
        },
        "metrics": {
            "total_requests": len(all_results),
            "successful_requests": len(successful_results),
            "failed_requests": len(all_results) - len(successful_results),
            "average_latency": avg_latency,
            "total_latency": total_latency,
            "throughput": throughput
        },
        "detailed_results": all_results
    }

    # Save results
    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(args.output)
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)

    with open(args.output, 'w') as f:
        json.dump(final_results, f, indent=2)

    print(f"\nResults saved to {args.output}")
    print(f"Average latency: {avg_latency:.3f}s")
    print(f"Throughput: {throughput:.2f} requests/second")
    print(f"Success rate: {len(successful_results)/len(all_results)*100:.1f}%")


if __name__ == "__main__":
    main()
