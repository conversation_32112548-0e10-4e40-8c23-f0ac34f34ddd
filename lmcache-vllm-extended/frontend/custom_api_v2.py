"###schedular based on the OpenAI API###"

import asyncio, time, json, numpy as np
from collections import defaultdict
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional

import vllm.entrypoints.openai.api_server as base_api
from vllm.entrypoints.openai.protocol import ChatCompletionRequest, ChatCompletionResponse
from fastapi import APIRouter, Request

# -------- Router 对外暴露 ----------
extended_router = APIRouter()
scheduler: "BatchScheduler" = None    

# --- GET /v2/models 直接复用 v1 ---
@extended_router.get("/models")
async def v2_models(request: Request):
    return await base_api.show_available_models(request)

# --- POST /v2/chat/completions — 入口 ---
@extended_router.post("/chat/completions")
async def create_chat_completion(req: ChatCompletionRequest, raw_request: Request):
    internal_req = to_internal_request(req)      
    fut          = await scheduler.enqueue(internal_req)  
    resp         = await fut                          


# -------- 1. 内部请求对象 ----------
@dataclass
class InternalReq:
    req_id: str
    context: str
    question: str
    seq_len: int
    context_hash: str
    orig_request: ChatCompletionRequest
    future: Optional[asyncio.Future] = field(default=None)


# -------- 2. 外部 -> 内部 转换 ----------
_request_cnt = 0
def to_internal_request(req: ChatCompletionRequest) -> InternalReq:
    global _request_cnt
    _request_cnt += 1

    user_msg  = req.messages[-1].content
    if "\n\n" in user_msg:
        context, question = user_msg.split("\n\n", 1)
    else:
        context, question = "", user_msg
    seq_len   = len(user_msg)                       # 简易 token 估计
    ctx_hash  = str(hash(context))
    return InternalReq(
        req_id=f"r{_request_cnt}",
        context=context,
        question=question,
        seq_len=seq_len,
        context_hash=ctx_hash,
        orig_request=req
    )


# -------- 3. BatchScheduler ----------
class BatchScheduler:
    def __init__(self, max_batch_size:int=16, batch_timeout_ms:int=25):
        self.queue: asyncio.Queue[InternalReq] = asyncio.Queue()
        self.max_batch_size = max_batch_size
        self.batch_timeout  = batch_timeout_ms / 1000
        self.processed_ctxs: set[str] = set()        # 记录已命中 context
        asyncio.create_task(self._worker())          # 后台启动

    # 入口：单条请求入队 & 返回 Future
    async def enqueue(self, req: InternalReq) -> asyncio.Future:
        fut = asyncio.get_running_loop().create_future()
        req.future = fut
        await self.queue.put(req)
        return fut

    # 后台批处理主循环
    async def _worker(self):
        while True:
            first = await self.queue.get()           # 至少取 1 条
            batch = [first]
            start = time.monotonic()

            # 在时间窗 / 数量窗内尽量多收集
            while len(batch) < self.max_batch_size:
                remain = self.batch_timeout - (time.monotonic() - start)
                if remain <= 0:
                    break
                try:
                    nxt = await asyncio.wait_for(self.queue.get(), timeout=remain)
                    batch.append(nxt)
                except asyncio.TimeoutError:
                    break

            # 调度排序
            ordered = self._reorder(batch)

            # 一次调用 vLLM
            responses = await self._inference_vllm(ordered)

            # 写回结果
            for r, req in zip(responses, ordered):
                if not req.future.done():
                    req.future.set_result(r)

    # ------ 调度策略：同 context 连续 + 缓存优先 ------
    def _reorder(self, batch: List[InternalReq]) -> List[InternalReq]:
        buckets: Dict[str, List[InternalReq]] = defaultdict(list)
        for r in batch:
            buckets[r.context_hash].append(r)

        cached, new = [], []
        for h, lst in buckets.items():
            (cached if h in self.processed_ctxs else new).append(lst)

        cached.sort(key=len, reverse=True)                               # 缓存大桶先
        new.sort(key=lambda lst: np.mean([r.seq_len for r in lst]))       # 短平均先
        ordered = [req
                   for group in (cached + new)
                   for req in sorted(group, key=lambda r: r.seq_len)]     # 组内短→长

        # 更新缓存集合
        for req in ordered:
            self.processed_ctxs.add(req.context_hash)
        return ordered

    # ------ 与 vLLM 对接：串行 ------
    async def _inference_vllm(self, reqs: List[InternalReq]) -> List[ChatCompletionResponse]:
        results: List[ChatCompletionResponse] = []
        for internal in reqs:
            resp = await base_api.create_chat_completion(internal.orig_request, Request)  # type: ignore
            results.append(resp)
        return results


scheduler = BatchScheduler(max_batch_size=16, batch_timeout_ms=25)
