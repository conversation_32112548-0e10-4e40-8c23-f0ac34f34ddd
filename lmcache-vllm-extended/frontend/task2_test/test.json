{"experiment_config": {"num_batches": 2, "batch_size": 5, "repeat_ratio": 0.4, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null, "api_version": "v1", "scheduling": "client-side"}, "metrics": {"total_requests": 10, "successful_requests": 10, "failed_requests": 0, "average_latency": 0.2909178972244263, "total_latency": 2.9091789722442627, "throughput": 3.43739594415038}, "detailed_results": [{"request_id": "req_1", "context_hash": "2585638493310769692", "sequence_length": 451, "latency": 1.4109196662902832, "success": true, "timestamp": 1748595252.5750053, "batch_id": "batch_52574", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-1645224207689444999", "sequence_length": 505, "latency": 0.18534612655639648, "success": true, "timestamp": 1748595253.9859676, "batch_id": "batch_52574", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "2585638493310769692", "sequence_length": 451, "latency": 0.06421589851379395, "success": true, "timestamp": 1748595254.1713476, "batch_id": "batch_52574", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-564385257547210457", "sequence_length": 506, "latency": 0.0670156478881836, "success": true, "timestamp": 1748595254.2355933, "batch_id": "batch_52574", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-564385257547210457", "sequence_length": 505, "latency": 0.7272751331329346, "success": true, "timestamp": 1748595254.3026404, "batch_id": "batch_52574", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "-687157447959359681", "sequence_length": 469, "latency": 0.16359353065490723, "success": true, "timestamp": 1748595255.0301332, "batch_id": "batch_52574", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-564385257547210457", "sequence_length": 505, "latency": 0.09982728958129883, "success": true, "timestamp": 1748595255.1937578, "batch_id": "batch_52574", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "8944261608101064070", "sequence_length": 483, "latency": 0.07378506660461426, "success": true, "timestamp": 1748595255.2936137, "batch_id": "batch_52574", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-564385257547210457", "sequence_length": 502, "latency": 0.06965088844299316, "success": true, "timestamp": 1748595255.367427, "batch_id": "batch_52574", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-564385257547210457", "sequence_length": 505, "latency": 0.04754972457885742, "success": true, "timestamp": 1748595255.4371061, "batch_id": "batch_52574", "batch_index": 1, "processing_mode": "sequential"}]}