#!/usr/bin/env python3
# task4_producer.py

import os
import sys
import json
import requests

# ———— 配置区域 ————
API_URL     = "http://192.168.2.31:8000/v2/chat/completions"
BASE_DIR    = os.path.dirname(os.path.abspath(__file__))
PARQUET_FILE = os.path.join(BASE_DIR, "gererated_queries.parquet")
DATA_DIR     = os.path.join(BASE_DIR, "data")  
OUTPUT_META  = os.path.join(BASE_DIR, "meta.jsonl")
# ——————————————————

def load_queries(parquet_path):
    """读取 parquet，把 index 当 label，第一列当 prompt"""
    try:
        import pyarrow.parquet as pq
    except ImportError:
        print("错误：缺少 pyarrow。请运行 pip install pyarrow")
        sys.exit(1)

    # 读表
    table = pq.read_table(parquet_path)
    df = table.to_pandas()
    if df.shape[1] < 1:
        print("错误：parquet 文件至少应有一列 query 内容")
        sys.exit(1)

    recs = []
    # DataFrame 的 index 就是 var (你的文章名/ID)
    # 第一列用 df.columns[0]
    col = df.columns[0]
    for label, row in df.iterrows():
        prompt = str(row[col])
        recs.append({"label": str(label), "prompt": prompt})
    return recs

def main():
    # 1. 加载 queries
    if not os.path.exists(PARQUET_FILE):
        print(f"错误：parquet 文件不存在：{PARQUET_FILE}")
        sys.exit(1)
    queries = load_queries(PARQUET_FILE)
    print(f"Loaded {len(queries)} queries from {PARQUET_FILE}")

    # 2. 逐条请求并写 meta.jsonl
    with open(OUTPUT_META, "w", encoding="utf-8") as fout:
        for idx, rec in enumerate(queries, 1):
            prompt = rec["prompt"]
            label  = rec["label"]

            # 可选：校验 context 文件是否存在
            ctx_file = os.path.join(DATA_DIR, f"{label}.txt")
            if not os.path.exists(ctx_file):
                print(f"[WARNING] Context not found: {ctx_file}")

            payload = {
                "model": "Qwen/Qwen2.5-1.5B-Instruct",
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": 1000
            }
            try:
                resp = requests.post(API_URL, json=payload, timeout=30)
                resp.raise_for_status()
                data = resp.json()
            except Exception as e:
                print(f"[ERROR] 请求失败 ({label}): {e}")
                continue

            kv_path = data.get("kv_path", "")
            if not kv_path:
                print(f"[ERROR] 返回中未含 kv_path ({label})")
                continue

            out_rec = {"prompt": prompt, "label": label, "kv_path": kv_path}
            fout.write(json.dumps(out_rec, ensure_ascii=False) + "\n")
            print(f"[{idx}/{len(queries)}] {label} → {kv_path}")

    print(f"Done. Wrote metadata to {OUTPUT_META}")

if __name__ == "__main__":
    main()
