#!/usr/bin/env python3
"""
脚本：按顺序读取data文件夹中的txt文件并发送API请求
每个文件处理完后需要手动输入"next"继续
"""

import requests
import time
from pathlib import Path

# 配置
API_URL = "http://192.168.2.31:8000/v2/chat/completions"
MODEL = "Qwen/Qwen2.5-1.5B-Instruct"
DATA_DIR = "lmcache-vllm-extended/frontend/data"
MAX_FILES = 15

def main():
    # 获取所有txt文件并排序
    data_path = Path(DATA_DIR)
    if not data_path.exists():
        print(f"错误：数据目录 {DATA_DIR} 不存在")
        return

    txt_files = sorted(data_path.glob("*.txt"))

    print(f"找到 {len(txt_files)} 个txt文件")
    print("开始按顺序处理文件...")
    print("注意：每处理完一个文件后，需要输入 'next' 继续下一个文件")
    print("=" * 60)

    for i, file_path in enumerate(txt_files[:MAX_FILES], 1):
        print(f"\n第 {i} 个文件: {file_path.name}")
        print("-" * 40)

        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            print(f"文件内容长度: {len(content)} 字符")
            print(f"内容预览: {content[:150]}...")

            # 构建请求数据
            request_data = {
                "model": MODEL,
                "messages": [
                    {"role": "user", "content": content}
                ],
                "max_tokens": 1000
            }

            # 发送请求
            print(f"\n发送请求到 {API_URL}")
            start_time = time.time()

            response = requests.post(
                API_URL,
                headers={"Content-Type": "application/json"},
                json=request_data,
                timeout=300
            )

            end_time = time.time()
            response_time = end_time - start_time

            if response.status_code == 200:
                response_json = response.json()

                # 提取并显示响应内容
                if 'choices' in response_json and len(response_json['choices']) > 0:
                    response_content = response_json['choices'][0]['message']['content']

                    print(f"✓ 请求成功完成")
                    print(f"  HTTP状态码: {response.status_code}")
                    print(f"  响应时间: {response_time:.2f}s")
                    print(f"  响应长度: {len(response_content)} 字符")
                    print(f"\n响应内容:")
                    print("-" * 40)
                    print(response_content)
                    print("-" * 40)
                else:
                    print(f"✗ 响应格式异常")
                    print(f"完整响应: {response.text}")
            else:
                print(f"✗ 请求失败")
                print(f"  HTTP状态码: {response.status_code}")
                print(f"  错误信息: {response.text}")

        except Exception as e:
            print(f"✗ 处理文件时发生错误: {str(e)}")

        # 如果不是最后一个文件，等待用户输入
        if i < len(txt_files) and i < MAX_FILES:
            print(f"\n已完成第 {i} 个文件的处理")
            while True:
                user_input = input("输入 'next' 继续下一个文件，或 'quit' 退出: ").strip().lower()
                if user_input == 'next':
                    break
                elif user_input == 'quit':
                    print("用户选择退出")
                    return
                else:
                    print("请输入 'next' 或 'quit'")

    print("\n" + "=" * 50)
    print("所有文件处理完成！")
    print("=" * 50)

if __name__ == "__main__":
    main()
