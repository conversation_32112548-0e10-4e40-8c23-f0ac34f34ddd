{"id": "chat-46b5169bf38543cba4f3c3e436de9a9b", "object": "chat.completion", "created": 1748953450, "model": "Qwen/Qwen2.5-1.5B-Instruct", "choices": [{"index": 0, "message": {"role": "assistant", "content": "### 1. Introduction\nLarge Language Models (LLMs) in Retrieval-Augmented Generation (RAG) settings often require multiple text chunks for context, leading to increased prefill latency. Traditional cache reuse strategies, such as prefix or full reuse, fail to balance speed and generation quality due to missing cross-attention. CacheBlend is introduced to selectively recompute a small fraction of tokens to preserve cross-attention efficiently, improving both speed and quality.\n\n### 2. Background\nLLMs generate KV (key-value) caches during prefill, and traditional methods like prefix caching are limited to first-chunk reuse. Full KV reuse ignores token dependencies between chunks, causing accuracy drops. CacheBlend proposes selective recomputation of high-KV-deviation (HKVD) tokens to restore fidelity while maintaining speed.\n\n### 3. Motivation\nPrefix caching saves little when multiple text chunks are used. Full KV reuse breaks quality due to absent cross-attention. CacheBlend proposes selective recomputation of high-KV-deviation tokens to restore fidelity while maintaining speed.\n\n### 4. Fast KV Cache Fusing\n- **4.1 Terminology**: Defines KV deviation and attention deviation.\n- **4.2 Selective Recompute**: Recomputes KV for selected tokens per layer to reduce compute load.\n- **4.3 Token Selection**: Identifies HKVD tokens by computing attention deviation layer-wise using a gradual filtering scheme.\n\n### 5. CacheBlend System Design\n- **5.1 Key Components**: \n  - **Loading Controller**: Estimates recompute vs load delay to choose optimal KV recompute ratio and storage device.\n  - **KV Cache Store**: Maps input chunks to precomputed KV caches.\n  - **Fusor**: Merges caches via layer-wise recomputation.\n- **5.2 Workflow**: Chunks are fetched, selectively recomputed, and pipelined for high efficiency.\n\n### 6. Implementation\nCacheBlend is built on vLLM with 3K lines of PyTorch code. It integrates three interfaces—`fetch_kv`, `prefill_layer`, and `synchronize`—for loading, partial recompute, and layer coordination. Prefilled KV caches can be stored and retrieved from RAM or disk.\n\n### 7. Evaluation\n- **7.1 Setup**: Evaluated on Mistral-7B, Yi-34B, and Llama-70B using datasets like 2WikiMQA, Musique, SAMSum, and MultiNews.\n- **7.2 Overall Improvement**: Up to 3.3× faster TTFT than full recompute, significantly higher quality than full KV reuse.\n- **7.3 Sensitivity Analysis**: CacheBlend performs consistently across different chunk lengths, sizes, batch sizes, and recompute ratios (5–18%).\n\n### 8. Related Work\nCacheBlend contrasts with prefix caching, PromptCache, vLLM, and other RAG or context compression techniques. CacheBlend uniquely supports KV fusion with partial recomputation and is extensible to slow storage systems.\n\n### 9. Limitations\nCacheBlend may not generalize to non-transformer architectures (e.g., Mamba, Griffin). Evaluation was not conducted on latest serving engines (e.g., DistServe), but integration is future work.\n\n### 10. Conclusion\nCacheBlend enables reuse of cached KV chunks in non-prefix locations via selective recomputation. It maintains generation quality while drastically reducing latency and increasing throughput.", "tool_calls": []}, "logprobs": null, "finish_reason": "stop", "stop_reason": null}], "usage": {"prompt_tokens": 945, "total_tokens": 1651, "completion_tokens": 706}, "prompt_logprobs": null}