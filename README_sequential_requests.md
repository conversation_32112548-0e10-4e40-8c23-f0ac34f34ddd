# 顺序API请求脚本使用说明

## 概述
这些脚本用于按顺序读取 `lmcache-vllm-extended/frontend/data` 文件夹中的txt文件内容，并将每个文件的内容作为消息发送到指定的API端点。每处理完一个文件后，需要手动输入"next"才会继续下一个文件。

## 文件说明

### 1. run_sequential_requests.sh (Bash版本)
- **功能**: 使用curl命令发送HTTP请求，直接在终端显示响应
- **优点**: 无需额外依赖，系统自带
- **特点**: 支持手动控制处理进度

### 2. run_sequential_requests.py (Python版本)
- **功能**: 使用Python requests库发送HTTP请求，直接在终端显示响应
- **优点**: JSON处理更简单，错误处理更完善
- **特点**: 支持手动控制处理进度

## 使用方法

### 运行Bash脚本
```bash
./run_sequential_requests.sh
```

### 运行Python脚本
```bash
# 确保安装了requests库
pip install requests

# 运行脚本
./run_sequential_requests.py
# 或者
python3 run_sequential_requests.py
```

## 脚本功能

1. **自动发现文件**: 扫描 `lmcache-vllm-extended/frontend/data` 目录下的所有txt文件
2. **按顺序处理**: 按文件名排序，依次处理每个文件
3. **发送API请求**: 将文件内容作为用户消息发送到API
4. **直接显示响应**: 在终端直接显示API响应内容，不保存文件
5. **手动控制**: 每处理完一个文件后，需要输入"next"继续或"quit"退出

## 交互方式

脚本运行时会：
1. 显示当前处理的文件信息
2. 发送API请求并显示响应
3. 等待用户输入：
   - 输入 `next` 继续下一个文件
   - 输入 `quit` 退出脚本

## 配置参数

### API设置
- **URL**: `************:8000/v2/chat/completions`
- **模型**: `Qwen/Qwen2.5-1.5B-Instruct`
- **最大令牌数**: 1000

### 其他设置
- **最大处理文件数**: 15个
- **超时时间**: 300秒
- **交互模式**: 手动控制处理进度

## 当前数据文件

脚本将处理以下15个txt文件：
1. ServerlessLLM_summary.txt
2. SplitRPC-sigmetrics23.txt
3. aplomb-sigcomm12.txt
4. cacheblend.txt
5. click.txt
6. context.txt
7. metron-nsdi18.txt
8. nsdi20-paper-barbette.txt
9. nsdi22-paper-reda_1.txt
10. osdi24-agrawal.txt
11. osdi24-lee.txt
12. osdi24-sun-biao.txt
13. sigcomm2023_janus.txt
14. sigcomm24-crux.txt
15. vllm.txt

## 注意事项

1. **网络连接**: 确保能够访问 `************:8000`
2. **文件权限**: 确保脚本有执行权限
3. **API可用性**: 确保目标API服务正在运行
4. **交互操作**: 每个文件处理完后需要手动输入"next"继续

## 故障排除

### 常见问题
1. **连接超时**: 检查网络连接和API服务状态
2. **权限错误**: 使用 `chmod +x` 添加执行权限
3. **Python依赖**: 安装 `requests` 库: `pip install requests`

### 使用提示
- 脚本会在终端直接显示API响应内容
- 可以随时输入"quit"退出脚本
- 响应内容不会保存到文件，只在终端显示
- 每个文件的处理状态会实时显示

## 自定义修改

如需修改配置，请编辑脚本中的以下变量：
- `API_URL`: API端点地址
- `MODEL`: 使用的模型名称
- `DATA_DIR`: 数据文件目录
- `MAX_FILES`: 最大处理文件数

## 示例运行流程

```
$ ./run_sequential_requests.py

找到 15 个txt文件
开始按顺序处理文件...
注意：每处理完一个文件后，需要输入 'next' 继续下一个文件
============================================================

第 1 个文件: ServerlessLLM_summary.txt
----------------------------------------
文件内容长度: 1234 字符
内容预览: This paper presents ServerlessLLM...

发送请求到 http://************:8000/v2/chat/completions
✓ 请求成功完成
  HTTP状态码: 200
  响应时间: 2.34s
  响应长度: 567 字符

响应内容:
----------------------------------------
这篇论文介绍了ServerlessLLM...
----------------------------------------

已完成第 1 个文件的处理
输入 'next' 继续下一个文件，或 'quit' 退出: next

第 2 个文件: SplitRPC-sigmetrics23.txt
...
```
